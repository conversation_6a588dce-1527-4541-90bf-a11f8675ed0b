import os
import json
import tomllib
import time
import random
import uuid
import httpx
import idna
from loguru import logger
from urllib.parse import quote
from typing import Optional, Dict, Any, List
import asyncio

# 创建异步HTTP客户端
async def get_httpx_client():
    return httpx.AsyncClient(
        timeout=httpx.Timeout(connect=10.0, read=20.0, write=20.0, pool=5.0),
        verify=False,  # 禁用SSL验证
        follow_redirects=True,
        limits=httpx.Limits(max_connections=20, max_keepalive_connections=10),
    )

# 定义HTTP响应类，用于兼容之前的代码
class HttpResponse:
    def __init__(self, status_code, body, headers=None):
        self.code = status_code
        self.status_code = status_code  # 兼容性
        self.body = body
        self.headers = headers or {}
        self._text = None

    def json(self):
        """解析 JSON 响应体"""
        if not self.body:
            return {}
        if isinstance(self.body, str):
            return json.loads(self.body)
        return json.loads(self.body.decode('utf-8'))

    @property
    def text(self):
        """获取响应文本"""
        if self._text is None:
            if isinstance(self.body, str):
                self._text = self.body
            else:
                self._text = self.body.decode('utf-8', errors='ignore')
        return self._text

    async def aread(self):
        """兼容异步读取方法"""
        return self.body

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

# 性能监控功能已移除
PERFORMANCE_MONITORING_ENABLED = False


class DoubaoVideoSearch(PluginBase):
    description = "豆包AI视频搜索插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "DoubaoVideoSearch"

    def __init__(self):
        # 必须先调用父类初始化
        super().__init__()

        # 读取配置
        config = {}
        try:
            config_path = "plugins/DoubaoVideoSearch/config.toml"
            if os.path.exists(config_path):
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get("DoubaoVideoSearch", {})
            else:
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在，将使用默认配置")
        except Exception as e:
            logger.warning(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}，将使用默认配置")

        # 基本配置初始化
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["找视频", "搜视频", "视频搜索"])
        self.select_command = config.get("select_command", ["播放", "选择", "看"])  # 选择视频命令
        self.admin_command = config.get("admin_command", ["视频搜索状态", "视频搜索报告"])  # 管理员命令
        self.command_format = config.get("command-format", """
🎬豆包视频搜索插件使用说明：
找视频 <关键词> - 搜索相关视频
播放 <序号> - 播放指定序号的视频
""")

        # 确认表情包配置
        self.confirm_emoji = config.get("confirm_emoji", True)
        self.emoji_md5 = config.get("emoji_md5", "ae672c700aaf271a151e18a9ecf4445b")
        self.emoji_size = config.get("emoji_size", 30259)

        # API配置
        self.api_config = config.get("API", {})
        self.api_base_url = "https://www.doubao.com/samantha/chat/completion"
        self.cookies = self.api_config.get("cookies", "").strip()

        # 豆包API常量 - 使用与doubao_chat.py一致的设备ID
        self.device_id = "7532989318484657699"
        self.tea_uuid = "7532989324985157172"
        self.web_id = "7532989324985157172"

        # httpx客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()

        # 移除令牌桶和用户限制机制

        # 视频搜索去重缓存 - 存储每个查询关键词已发送过的视频URL
        self._video_cache = {}  # 格式: {query_key: {wxid: [sent_urls]}}
        self._video_cache_lock = asyncio.Lock()

        # 视频搜索结果缓存 - 存储用户的搜索结果供选择
        self._search_results = {}  # 格式: {wxid: {query: [videos], timestamp: time}}
        self._search_results_lock = asyncio.Lock()
        self._search_cache_timeout = 300  # 搜索结果缓存5分钟



        # 输出初始化调试信息
        logger.info(f"[{self.plugin_name}] 插件初始化完成")
        logger.debug(f"[{self.plugin_name}] 配置信息:")
        logger.debug(f"  - 启用状态: {self.enable}")
        logger.debug(f"  - 命令列表: {self.command}")
        logger.debug(f"  - 设备ID: {self.device_id}")
        logger.debug(f"  - Web ID: {self.web_id}")
        logger.debug(f"  - Cookies配置: {'已配置' if self.cookies else '未配置'}")
        logger.debug(f"  - 限制机制: 已禁用")






    async def _get_unique_video(self, videos: list, query_key: str, wxid: str) -> Optional[dict]:
        """从视频列表中获取一个未发送过的视频

        Args:
            videos: 视频列表
            query_key: 查询关键词（用于缓存）
            wxid: 群组或用户ID

        Returns:
            未发送过的视频信息，如果都发送过则返回None
        """
        async with self._video_cache_lock:
            # 初始化缓存结构
            if query_key not in self._video_cache:
                self._video_cache[query_key] = {}
            if wxid not in self._video_cache[query_key]:
                self._video_cache[query_key][wxid] = []

            sent_urls = self._video_cache[query_key][wxid]

            # 查找未发送过的视频
            for video in videos:
                video_url = video.get("url", "")
                if video_url and video_url not in sent_urls:
                    # 记录已发送的视频URL
                    sent_urls.append(video_url)
                    # 限制缓存大小，只保留最近的20个URL
                    if len(sent_urls) > 20:
                        sent_urls.pop(0)
                    return video

            # 如果所有视频都发送过，清空缓存并返回第一个视频
            if videos:
                self._video_cache[query_key][wxid] = [videos[0].get("url", "")]
                return videos[0]

            return None

    async def _cache_search_results(self, wxid: str, query: str, videos: list):
        """缓存搜索结果供用户选择

        Args:
            wxid: 群组或用户ID
            query: 搜索关键词
            videos: 视频列表
        """
        async with self._search_results_lock:
            current_time = time.time()
            if wxid not in self._search_results:
                self._search_results[wxid] = {}

            self._search_results[wxid][query] = {
                'videos': videos,
                'timestamp': current_time
            }

            # 清理过期的缓存
            for cached_query in list(self._search_results[wxid].keys()):
                if current_time - self._search_results[wxid][cached_query]['timestamp'] > self._search_cache_timeout:
                    del self._search_results[wxid][cached_query]

    async def _get_cached_video(self, wxid: str, query: str, index: int) -> Optional[dict]:
        """从缓存中获取指定索引的视频

        Args:
            wxid: 群组或用户ID
            query: 搜索关键词
            index: 视频索引（从1开始）

        Returns:
            视频信息字典，如果不存在则返回None
        """
        async with self._search_results_lock:
            if wxid not in self._search_results:
                return None

            if query not in self._search_results[wxid]:
                return None

            cached_data = self._search_results[wxid][query]
            current_time = time.time()

            # 检查缓存是否过期
            if current_time - cached_data['timestamp'] > self._search_cache_timeout:
                del self._search_results[wxid][query]
                return None

            videos = cached_data['videos']
            if 1 <= index <= len(videos):
                return videos[index - 1]  # 转换为0基索引

            return None



    async def call_doubao_video_search_api(self, bot: WechatAPIClient, message: dict, query: str) -> Optional[Dict[str, Any]]:
        """调用豆包视频搜索API

        Args:
            bot: 微信API客户端
            message: 消息对象
            query: 搜索查询关键词

        Returns:
            搜索结果字典，包含视频信息或错误信息
        """
        # 忽略未使用的参数
        _ = bot, message

        max_retries = 3  # 最大重试次数
        base_delay = 3  # 基础等待时间（秒）
        max_delay = 10  # 最大等待时间（秒）

        last_error = None
        conversation_id = None

        logger.info(f"[{self.plugin_name}] 开始视频搜索，关键词: {query}")
        logger.debug(f"[{self.plugin_name}] 调试信息 - 设备ID: {self.device_id}, Web ID: {self.web_id}")
        logger.debug(f"[{self.plugin_name}] 调试信息 - Cookies长度: {len(self.cookies) if self.cookies else 0}")

        for retry in range(max_retries):
            try:
                logger.debug(f"[{self.plugin_name}] 第{retry + 1}次尝试，开始构造请求参数")

                # 构造请求URL参数 - 与doubao_chat.py保持一致
                params = {
                    "aid": "497858",
                    "device_id": self.device_id,
                    "device_platform": "web",
                    "language": "zh",
                    "pc_version": "2.29.3",  # 与doubao_chat.py一致
                    "pkg_type": "release_version",
                    "real_aid": "497858",
                    "region": "CN",
                    "samantha_web": "1",
                    "sys_region": "CN",
                    "tea_uuid": self.tea_uuid,
                    "use-olympus-account": "1",
                    "version_code": "20800",
                    "web_id": self.web_id
                }

                # 创建随机的会话ID和消息ID - 每次请求都使用新的会话ID
                conversation_id = f"local_{int(time.time() * 1000)}"  # 与doubao_chat.py格式一致
                message_id = str(uuid.uuid4())  # 使用标准UUID格式

                logger.debug(f"[{self.plugin_name}] 生成会话ID: {conversation_id}, 消息ID: {message_id}")

                # 构造视频搜索查询 - 尝试更自然的查询方式
                search_query = f"帮我找一些关于{query}的抖音视频"
                logger.debug(f"[{self.plugin_name}] 构造搜索查询: {search_query}")

                # 记录当前视频查询关键词用于去重
                self._current_video_query = query

                # 构造请求数据 - 与doubao_chat.py保持一致
                request_data = {
                    "messages": [{
                        "content": json.dumps({
                            "text": search_query
                        }),
                        "content_type": 2001,
                        "attachments": [],
                        "references": []
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": True,
                        "need_create_conversation": True,  # 与doubao_chat.py一致
                        "launch_stage": 1,
                        "is_replace": False,
                        "is_delete": False,
                        "message_from": 0,
                        "use_deep_think": False,
                        "use_auto_cot": True,
                        "resend_for_regen": False,
                        "event_id": "0"
                    },
                    "evaluate_option": {
                        "web_ab_params": ""
                    },
                    "conversation_id": "0",  # 与doubao_chat.py一致
                    "local_conversation_id": conversation_id,  # 添加本地会话ID
                    "local_message_id": message_id
                }

                # 获取httpx客户端
                client = await self.get_session()

                # 设置请求头
                headers = self._generate_headers()
                # 不需要重复设置Content-Type，_generate_headers()中已经设置了content-type

                logger.debug(f"[{self.plugin_name}] 请求头: {headers}")
                logger.debug(f"[{self.plugin_name}] 请求体: {json.dumps(request_data, ensure_ascii=False)}")

                # 发送异步请求
                try:
                    # 构造完整URL
                    url = f"{self.api_base_url}?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])
                    logger.debug(f"[{self.plugin_name}] 请求URL: {url[:100]}...")  # 只显示前100个字符
                    logger.debug(f"[{self.plugin_name}] 请求数据大小: {len(str(request_data))} 字符")

                    # 发送POST请求
                    response = await client.post(
                        url,
                        json=request_data,
                        headers=headers,
                        timeout=httpx.Timeout(connect=10.0, read=300.0, write=60.0, pool=10.0)
                    )

                    logger.debug(f"[{self.plugin_name}] 收到响应，状态码: {response.status_code}")
                    logger.debug(f"[{self.plugin_name}] 响应头: {dict(response.headers)}")
                    logger.debug(f"[{self.plugin_name}] 响应内容长度: {len(response.content)} 字节")

                    # 检查状态码
                    if response.status_code != 200:
                        error_text = response.content
                        logger.error(f"[{self.plugin_name}] API请求失败: 状态码={response.status_code}, 响应内容={error_text}")
                        raise ValueError(f"API请求失败: {response.status_code}")

                    logger.debug(f"[{self.plugin_name}] API请求成功，开始处理响应流")

                    # 创建自定义响应对象
                    http_response = HttpResponse(
                        status_code=response.status_code,
                        body=response.content,
                        headers=dict(response.headers)
                    )

                    # 处理响应
                    logger.debug(f"[{self.plugin_name}] 开始处理响应流数据")
                    result = await self._process_video_stream(http_response)

                    if result:
                        logger.info(f"[{self.plugin_name}] 视频搜索成功，结果类型: {result.get('type', 'unknown')}")
                        if result.get('type') == 'videos':
                            video_count = len(result.get('videos', []))
                            logger.debug(f"[{self.plugin_name}] 获取到 {video_count} 个视频结果")
                        # 请求完成后删除会话，防止服务器端资源泄漏
                        if conversation_id:
                            asyncio.create_task(self._delete_conversation(conversation_id))
                        return result

                    # 如果没有结果，但还有重试机会，继续重试
                    if retry < max_retries - 1:
                        wait_time = min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay)
                        logger.warning(f"[{self.plugin_name}] 第{retry + 1}次请求无结果，{wait_time:.1f}秒后重试")
                        await asyncio.sleep(wait_time)
                        continue

                    # 如果是最后一次重试，返回空结果而不是抛出异常
                    logger.error(f"[{self.plugin_name}] 所有重试均无结果，返回空响应")
                    return {"type": "text", "text": ""}

                except httpx.RequestError as e:
                    raise ValueError(f"HTTP请求失败: {str(e)}")

            except ValueError as e:
                last_error = e
                logger.warning(f"[{self.plugin_name}] 第{retry + 1}次请求失败: {str(e)}")
                if retry < max_retries - 1:
                    wait_time = min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay)
                    logger.info(f"[{self.plugin_name}] {wait_time:.1f}秒后进行第{retry + 2}次重试")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    # 请求失败后也尝试删除会话，但返回空结果而不是抛出异常
                    logger.error(f"[{self.plugin_name}] 所有重试均失败，返回空响应")
                    if conversation_id:
                        asyncio.create_task(self._delete_conversation(conversation_id))
                    return {"type": "text", "text": ""}
            except Exception as e:
                last_error = e
                if retry < max_retries - 1:
                    wait_time = min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay)
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    # 请求失败后也尝试删除会话
                    if conversation_id:
                        asyncio.create_task(self._delete_conversation(conversation_id))
                    raise

        # 如果所有重试都失败，抛出最后一个错误
        if last_error:
            raise last_error

        raise ValueError("达到最大重试次数，等会再试试")

    async def _delete_conversation(self, conversation_id: str) -> bool:
        """删除豆包会话

        Args:
            conversation_id: 会话ID

        Returns:
            bool: 删除是否成功
        """
        try:
            # 构造URL参数
            params = {
                "version_code": "20800",
                "language": "zh",
                "device_platform": "web",
                "aid": "497858",
                "real_aid": "497858",
                "pc_version": "2.16.1",
                "pkg_type": "release_version",
                "device_id": self.device_id,
                "web_id": self.web_id,
                "tea_uuid": self.tea_uuid,
                "use-olympus-account": "1",
                "region": "CN",
                "sys_region": "CN",
                "samantha_web": "1"
            }

            # 构造请求URL
            url = f"https://www.doubao.com/samantha/thread/delete?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])

            # 构造请求头 - 与doubao_chat.py保持一致
            headers = {
                "Host": "www.doubao.com",
                "Connection": "keep-alive",
                "sec-ch-ua-platform": '"Android"',
                "sec-ch-ua": '"Chromium";v="130", "Android WebView";v="130", "Not?A_Brand";v="99"',
                "sec-ch-ua-mobile": "?1",
                "User-Agent": "Mozilla/5.0 (Linux; Android 15; PJD110 Build/AP3A.240617.008) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36",
                "content-type": "application/json",  # 改为小写，与doubao_chat.py一致
                "Accept": "*/*",  # 改为*/*，与doubao_chat.py一致
                "Origin": "https://www.doubao.com",
                "X-Requested-With": "mark.via",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "Referer": "https://www.doubao.com/chat/",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "Cookie": self.cookies
            }

            # 构造请求数据
            data = {"conversation_id": conversation_id}

            # 获取httpx客户端
            client = await self.get_session()

            # 发送异步请求
            try:
                response = await client.post(
                    url,
                    json=data,
                    headers=headers,
                    timeout=httpx.Timeout(connect=5.0, read=10.0, write=10.0, pool=5.0)
                )

                # 如果状态码为200，我们认为删除成功，即使响应为空
                if response.status_code == 200:
                    # 检查响应文本是否为空
                    text = response.text
                    if not text or text.isspace():
                        return True

                    # 尝试解析JSON
                    try:
                        resp_data = response.json()
                        if resp_data.get("code") == 0:
                            return True
                        else:
                            return False
                    except json.JSONDecodeError:
                        # 如果JSON解析失败，但状态码为200，仍视为成功
                        return True
                else:
                    return False

            except httpx.RequestError:
                return False

        except Exception:
            return False

    async def _process_video_stream(self, response: HttpResponse) -> Optional[Dict[str, Any]]:
        """处理视频搜索的SSE响应流

        Args:
            response: HTTP响应对象

        Returns:
            处理后的响应数据字典
        """
        result_data = {"type": "text", "text": ""}
        videos_data = []
        is_video_search = False  # 标记是否是视频搜索请求

        logger.debug(f"[{self.plugin_name}] 开始处理SSE响应流")

        try:
            # 添加超时控制
            timeout = 300  # 设置300秒超时
            start_time = time.time()

            # 添加结束标志
            is_completed = False
            last_update_time = time.time()
            max_idle_time = 10  # 最大空闲时间（秒）

            # 解析SSE流 - 在httpx中需要一次性处理整个响应
            buffer = ""

            # 获取响应数据 - 确保是bytes类型
            if isinstance(response.body, str):
                chunk = response.body.encode('utf-8')
            else:
                chunk = response.body

            logger.debug(f"[{self.plugin_name}] 响应数据类型: {type(response.body)}, 大小: {len(chunk)} 字节")

            try:
                # 安全解码，忽略错误的字节
                try:
                    decoded_chunk = chunk.decode('utf-8', errors='ignore')
                    buffer = decoded_chunk
                    logger.debug(f"[{self.plugin_name}] 解码后数据长度: {len(buffer)} 字符")
                except UnicodeDecodeError:
                    # 跳过无法解码的数据，继续处理
                    logger.error(f"[{self.plugin_name}] 数据解码失败")
                    return None

                # 处理完整的SSE事件
                event_count = 0
                while "\n\n" in buffer:
                    parts = buffer.split("\n\n", 1)
                    event = parts[0]
                    buffer = parts[1]
                    event_count += 1

                    # 跳过空事件
                    if not event.strip():
                        continue

                    # 提取"data:"行
                    data_line = None
                    for line in event.split("\n"):
                        if line.startswith("data:"):
                            data_line = line[5:].strip()
                            break

                    if not data_line:
                        continue

                    logger.debug(f"[{self.plugin_name}] 处理第 {event_count} 个事件，数据长度: {len(data_line)}")

                    # 解析数据
                    try:
                        event_data = json.loads(data_line)
                        if not isinstance(event_data, dict):
                            continue

                        # 处理错误消息
                        if "event_type" not in event_data:
                            continue

                        event_type = event_data["event_type"]
                        logger.debug(f"[{self.plugin_name}] 事件类型: {event_type}")

                        # 处理结束事件
                        if event_type == 2003:  # 结束事件
                            logger.debug(f"[{self.plugin_name}] 收到结束事件")
                            is_completed = True

                            # 如果有视频数据，优先返回视频结果
                            if videos_data:
                                logger.info(f"[{self.plugin_name}] 结束事件：返回 {len(videos_data)} 个视频结果")
                                result_data = {
                                    "type": "videos",
                                    "videos": videos_data
                                }
                                return result_data

                            # 否则返回文本结果
                            logger.debug(f"[{self.plugin_name}] 结束事件：返回文本结果")
                            return result_data

                        # 错误事件
                        if event_type == 2005:
                            try:
                                error_data = json.loads(event_data["event_data"])
                                logger.warning(f"[{self.plugin_name}] 收到错误事件: {error_data}")
                                # 记录错误，但不做特殊处理
                                pass
                            except Exception:
                                logger.warning(f"[{self.plugin_name}] 错误事件解析失败")
                                pass

                        # 正常事件 - seed_intention type
                        if event_type == 2010 and "event_data" in event_data:
                            try:
                                inner_data = json.loads(event_data["event_data"])
                                if "type" in inner_data and inner_data["type"] == "seed_intention":
                                    seed_info = inner_data.get("seed_intention", {})
                                    if seed_info.get("intention") == "browsing" and seed_info.get("detail") == "rich_media_only_video":
                                        is_video_search = True
                                        logger.debug(f"[{self.plugin_name}] 检测到视频搜索意图")
                            except Exception:
                                continue

                        # 正常事件
                        if event_type == 2001 and "event_data" in event_data:  # 消息事件
                            try:
                                inner_data = json.loads(event_data["event_data"])

                                if "message" not in inner_data:
                                    continue

                                message = inner_data["message"]
                                is_finish = inner_data.get("is_finish", False)

                                if "content_type" not in message or "content" not in message:
                                    continue

                                content_type = message["content_type"]
                                try:
                                    content = json.loads(message["content"])
                                except json.JSONDecodeError:
                                    continue

                                # 处理视频搜索内容
                                if content_type == 2007:  # 视频搜索内容
                                    logger.debug(f"[{self.plugin_name}] 收到视频搜索内容")
                                    # 处理视频卡片数据
                                    if "search_result" in content and "video_card" in content["search_result"]:
                                        video_card = content["search_result"]["video_card"]
                                        if "card_list" in video_card:
                                            logger.debug(f"[{self.plugin_name}] 找到 {len(video_card['card_list'])} 个视频卡片")
                                            for i, video_item in enumerate(video_card["card_list"]):
                                                # 提取视频信息
                                                video_info = {
                                                    "item_id": video_item.get("item_id", ""),
                                                    "title": video_item.get("video_captions", "未知标题"),
                                                    "url": video_item.get("main_site_url", ""),
                                                    "thumb_url": video_item.get("video_first_frame_image", video_item.get("album_image", "")),
                                                    "source": video_item.get("source_app_name", "抖音"),
                                                    "description": f"来源: {video_item.get('source_app_name', '抖音')}"
                                                }

                                                logger.debug(f"[{self.plugin_name}] 视频 {i+1}: {video_info['title'][:30]}...")

                                                # 避免重复添加
                                                if video_info not in videos_data:
                                                    videos_data.append(video_info)

                                            # 标记为视频类型响应
                                            if videos_data:
                                                result_data = {
                                                    "type": "videos",
                                                    "videos": videos_data
                                                }
                                                logger.debug(f"[{self.plugin_name}] 累计收集到 {len(videos_data)} 个视频")

                                            # 如果是完成状态，立即返回结果
                                            if is_finish and videos_data:
                                                logger.info(f"[{self.plugin_name}] 完成状态：立即返回 {len(videos_data)} 个视频结果")
                                                return result_data

                            except Exception as e:
                                continue

                        # 更新最后活动时间
                        last_update_time = time.time()

                    except json.JSONDecodeError as e:
                        continue
                    except Exception as e:
                        continue

                    # 检查是否超时
                    if time.time() - start_time > timeout:
                        if videos_data:
                            result_data = {
                                "type": "videos",
                                "videos": videos_data
                            }
                            return result_data
                        break

                    # 检查是否长时间没有新内容
                    if time.time() - last_update_time > max_idle_time:
                        if videos_data:
                            result_data = {
                                "type": "videos",
                                "videos": videos_data
                            }
                            return result_data

            except Exception as e:
                # 继续处理，尝试返回已收集的数据
                pass

            # 如果有视频数据，优先返回视频数据
            if videos_data:
                result_data = {
                    "type": "videos",
                    "videos": videos_data
                }
                return result_data

            # 如果是视频搜索但没有获取到视频数据
            if is_video_search and not videos_data:
                result_data = {
                    "type": "text",
                    "text": "视频搜索失败，请稍后重试"
                }
                return result_data

            # 修改响应处理逻辑，避免误判为失败
            if is_completed:
                # 即使没有内容也返回空字典，避免被误判为失败
                return {"type": "text", "text": ""}

            # 实在没有任何有效内容，返回None
            # 修改这里：返回空文本结果而不是None
            result_data["text"] = ""
            return result_data

        except Exception as e:
            # 如果已经有部分结果，尽可能返回
            if videos_data:
                result_data = {
                    "type": "videos",
                    "videos": videos_data
                }
                return result_data
            return None

    async def handle_video_message(self, bot: WechatAPIClient, message: dict, result: Dict[str, Any], query: str = ""):
        """处理视频消息 - 显示搜索结果列表

        Args:
            bot: 微信API客户端
            message: 消息对象
            result: 搜索结果
            query: 搜索关键词
        """
        try:
            logger.debug(f"[{self.plugin_name}] 开始处理视频消息，结果类型: {result.get('type', 'unknown')}")

            if result["type"] == "videos":
                # 处理视频消息
                videos = result["videos"]
                logger.info(f"[{self.plugin_name}] 收到{len(videos)}个视频结果")
                if not videos:
                    logger.warning(f"[{self.plugin_name}] 视频列表为空")
                    await bot.send_at_message(message["FromWxid"], "没有找到相关视频", [message["SenderWxid"]])
                    return

                # 缓存搜索结果
                await self._cache_search_results(message["FromWxid"], query, videos)

                # 构建聊天记录格式的视频列表
                record_xml = await self._build_video_record_xml(videos, query)

                # 发送聊天记录消息
                await self._send_video_record_message(bot, message["FromWxid"], record_xml, query, len(videos))

            else:
                # 如果不是视频类型，发送错误消息
                logger.warning(f"[{self.plugin_name}] 结果类型不是视频: {result.get('type', 'unknown')}")
                await bot.send_at_message(message["FromWxid"], "没有找到相关视频", [message["SenderWxid"]])

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理视频消息异常: {str(e)}")
            await bot.send_at_message(message["FromWxid"], "处理不了，等会再试试", [message["SenderWxid"]])

    async def _build_video_record_xml(self, videos: list, query: str) -> str:
        """构建视频列表的聊天记录XML

        Args:
            videos: 视频列表
            query: 搜索关键词

        Returns:
            聊天记录XML字符串
        """
        try:
            # 构建视频列表内容（一条聊天记录）
            max_videos = min(len(videos), 10)  # 最多显示前10个
            video_list_content = ""

            for i, video in enumerate(videos[:max_videos], 1):
                title = video.get('title', '未知标题')
                # 清理标题，移除多余的符号和换行
                title = title.replace('\n', ' ').replace('\r', ' ').strip()
                title = ' '.join(title.split())

                # 限制标题长度
                if len(title) > 60:
                    title = title[:60] + "..."

                video_list_content += f"{i}. {title}\n"

            if len(videos) > 10:
                video_list_content += f"\n... 还有 {len(videos) - 10} 个视频未显示"

            video_list_content += f"\n\n💡 回复数字选择视频（如：1）"

            # 调试：输出视频列表内容
            logger.debug(f"[{self.plugin_name}] 视频列表内容:\n{repr(video_list_content)}")

            # 构建描述
            desc = f"找到 {len(videos)} 个关于「{query}」的视频"
            if len(videos) > 10:
                desc += f"，显示前 {max_videos} 个"

            # 构建单条聊天记录
            data_id = f"video_list_{int(time.time())}"
            source_id = f"source_list_{int(time.time())}"
            user_hash = "video_search_bot"
            message_time = int(time.time() * 1000)
            user_avatar = "http://wx.qlogo.cn/mmhead/ver_1/0/0/132"

            data_item = f'''<dataitem datatype="1" dataid="{data_id}" datasourceid="{source_id}">
<datadesc><![CDATA[{video_list_content}]]></datadesc>
<sourcename>视频搜索助手</sourcename>
<sourceheadurl>{user_avatar}</sourceheadurl>
<sourcetime>2025-01-01&#x20;12:00:00</sourcetime>
<srcMsgCreateTime>{message_time}</srcMsgCreateTime>
<fromnewmsgid>{source_id}</fromnewmsgid>
<dataitemsource><hashusername>{user_hash}</hashusername></dataitemsource>
</dataitem>'''

            data_items = [data_item]

            # 组装完整的recordinfo XML
            record_xml = f'''<recordinfo>
<title>🎬 视频搜索结果</title>
<desc>{desc}</desc>
<datalist count="1">
{''.join(data_items)}
</datalist>
<favcreatetime>{int(time.time() * 1000)}</favcreatetime>
</recordinfo>'''

            return record_xml

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 构建聊天记录XML失败: {str(e)}")
            return ""

    async def _send_video_record_message(self, bot: WechatAPIClient, wxid: str, record_xml: str, query: str, total_count: int):
        """发送视频列表聊天记录消息

        Args:
            bot: 微信API客户端
            wxid: 接收者ID
            record_xml: 聊天记录XML
            query: 搜索关键词
            total_count: 视频总数
        """
        try:
            if not record_xml:
                await bot.send_text_message(wxid, "生成视频列表失败，请重试")
                return

            # 构建聊天记录消息XML格式 (Type 19)
            title = f"🎬 {query} - 视频搜索结果"
            description = f"找到 {total_count} 个视频，回复数字选择播放"

            xml = f'''<appmsg appid="" sdkver="0">
<title>{title}</title>
<des>{description}</des>
<action>view</action>
<type>19</type>
<showtype>0</showtype>
<content/>
<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>
<lowurl/>
<dataurl/>
<lowdataurl/>
<recorditem><![CDATA[{record_xml}]]></recorditem>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <aeskey/>
</appattach>
<extinfo/>
<sourceusername/>
<sourcedisplayname/>
<thumburl/>
<md5/>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''

            # 发送聊天记录消息
            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                19  # 聊天记录消息类型
            )

            if new_msg_id != 0 and client_msg_id:
                logger.info(f"[{self.plugin_name}] 视频列表聊天记录发送成功")
                # 发送提示消息
                await bot.send_text_message(wxid, "💡 回复数字选择视频（如：1）")
            else:
                logger.error(f"[{self.plugin_name}] 视频列表聊天记录发送失败")
                await bot.send_text_message(wxid, "发送视频列表失败，请重试")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送视频列表聊天记录异常: {str(e)}")
            await bot.send_text_message(wxid, "发送视频列表失败，请重试")

    async def handle_video_selection(self, bot: WechatAPIClient, message: dict, index: int, query: str = ""):
        """处理用户选择播放特定视频

        Args:
            bot: 微信API客户端
            message: 消息对象
            index: 视频索引（从1开始）
            query: 搜索关键词（可选，用于查找缓存）
        """
        try:
            wxid = message["FromWxid"]
            user_wxid = message["SenderWxid"]

            # 如果没有提供查询关键词，尝试从最近的缓存中查找
            selected_video = None
            used_query = query

            if query:
                # 使用指定的查询关键词
                selected_video = await self._get_cached_video(wxid, query, index)
            else:
                # 尝试从最近的搜索结果中查找
                async with self._search_results_lock:
                    if wxid in self._search_results:
                        # 按时间戳排序，获取最新的搜索结果
                        recent_queries = sorted(
                            self._search_results[wxid].items(),
                            key=lambda x: x[1]['timestamp'],
                            reverse=True
                        )

                        for query_key, data in recent_queries:
                            current_time = time.time()
                            if current_time - data['timestamp'] <= self._search_cache_timeout:
                                videos = data['videos']
                                if 1 <= index <= len(videos):
                                    selected_video = videos[index - 1]
                                    used_query = query_key
                                    break

            if not selected_video:
                await bot.send_at_message(wxid, f"没有找到第 {index} 个视频，请先搜索视频或检查序号", [user_wxid])
                return

            url = selected_video.get("url")
            if not url:
                logger.error(f"[{self.plugin_name}] 选中的视频缺少URL")
                await bot.send_at_message(wxid, "视频链接有问题，换一个试试", [user_wxid])
                return

            logger.info(f"[{self.plugin_name}] 用户选择播放视频 {index}: {selected_video.get('title', '未知标题')[:50]}...")

            # 解析视频链接
            video_info = await self.parse_douyin_video(url)
            if not video_info:
                # 如果解析失败，使用原始信息发送XML视频分享消息
                logger.warning(f"[{self.plugin_name}] 视频解析失败，使用原始信息发送")
                await self._send_video_share_message(
                    bot=bot,
                    wxid=wxid,
                    title=selected_video.get("title", "抖音视频"),
                    description=selected_video.get("description", "来源: 抖音"),
                    video_url=url,
                    thumb_url=selected_video.get("thumb_url", "")
                )
                return

            logger.info(f"[{self.plugin_name}] 视频解析成功: {video_info.get('song_name', '未知标题')}")

            # 使用解析后的信息发送XML视频分享消息
            title = video_info.get('song_name', '抖音视频')
            description = f"作者: {video_info.get('artist_name', '抖音用户')}"
            video_url = video_info['media_url']

            # 发送解析后的视频分享消息
            await self._send_video_share_message(
                bot=bot,
                wxid=wxid,
                title=title,
                description=description,
                video_url=video_url,
                thumb_url=video_info.get('img_url', '')
            )

            # 记录到去重缓存中
            if used_query:
                async with self._video_cache_lock:
                    if used_query not in self._video_cache:
                        self._video_cache[used_query] = {}
                    if wxid not in self._video_cache[used_query]:
                        self._video_cache[used_query][wxid] = []

                    sent_urls = self._video_cache[used_query][wxid]
                    if url not in sent_urls:
                        sent_urls.append(url)
                        # 限制缓存大小
                        if len(sent_urls) > 20:
                            sent_urls.pop(0)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理视频选择异常: {str(e)}")
            await bot.send_at_message(message["FromWxid"], "播放失败，等会再试试", [message["SenderWxid"]])

    async def _send_video_share_message(self, bot: WechatAPIClient, wxid: str, title: str, description: str, video_url: str, thumb_url: str = ""):
        """发送视频分享消息"""
        try:
            logger.debug(f"[{self.plugin_name}] 开始发送视频分享消息到 {wxid}")
            logger.debug(f"[{self.plugin_name}] 视频信息 - 标题: {title}, 描述: {description}")
            logger.debug(f"[{self.plugin_name}] 视频URL: {video_url[:100]}...")

            # 参考音乐消息格式的视频分享消息
            xml = f'''<appmsg appid="wx75f04c8595ccb9f6" sdkver="0">
<title>{title}</title>
<des>{description}</des>
<action>view</action>
<type>68</type>
<showtype>0</showtype>
<content/>
<url>{video_url}</url>
<dataurl/>
<lowurl>https://game.weixin.qq.com/</lowurl>
<lowdataurl/>
<recorditem/>
<thumburl>{thumb_url}</thumburl>
<messageaction/>
<laninfo/>
<extinfo/>
<sourceusername/>
<sourcedisplayname/>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <aeskey/>
</appattach>
<webviewshared>
    <publisherId/>
    <publisherReqId>0</publisherReqId>
</webviewshared>
<weappinfo>
    <pagepath/>
    <username/>
    <appid/>
    <appservicetype>0</appservicetype>
</weappinfo>
<websearch/>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''

            logger.debug(f"[{self.plugin_name}] XML消息长度: {len(xml)} 字符")

            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                68  # 视频分享消息类型
            )

            if new_msg_id == 0 or not client_msg_id:
                logger.error(f"[{self.plugin_name}] 视频分享消息发送失败，client_msg_id: {client_msg_id}, new_msg_id: {new_msg_id}")
            else:
                logger.info(f"[{self.plugin_name}] 视频分享消息发送成功，client_msg_id: {client_msg_id}, new_msg_id: {new_msg_id}")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 视频分享消息发送异常: {str(e)}")

    async def parse_douyin_video(self, video_url: str) -> Optional[dict]:
        """解析抖音视频链接

        Args:
            video_url: 抖音分享链接

        Returns:
            解析后的视频信息字典，包含以下字段：
            - media_url: 视频播放地址
            - artist_name: 作者名称
            - song_name: 视频标题
            - img_url: 视频封面
        """
        logger.debug(f"[{self.plugin_name}] 开始解析抖音视频: {video_url}")
        try:
            # 构造解析API URL（使用苏苏API，对域名进行编码处理）
            # 将中文域名转换为punycode编码
            try:
                # 尝试使用idna编码中文域名
                encoded_domain = idna.encode("api.苏苏.cn").decode('ascii')
                parse_url = f"http://{encoded_domain}/API/douyin.php?url={quote(video_url)}"
            except Exception:
                # 如果idna编码失败，使用IP地址或其他方式
                # 这里可以替换为实际的IP地址或其他可用域名
                parse_url = f"http://api.xn--9kr62g.cn/API/douyin.php?url={quote(video_url)}"

            # 构造请求头
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Connection": "keep-alive",
                "Referer": "http://api.xn--9kr62g.cn/",
                "Origin": "http://api.xn--9kr62g.cn"
            }

            # 获取httpx客户端
            client = await self.get_session()

            # 发送异步请求
            try:
                logger.debug(f"[{self.plugin_name}] 发送视频解析请求: {parse_url}")
                response = await client.get(
                    parse_url,
                    headers=headers,
                    timeout=httpx.Timeout(connect=10.0, read=30.0, write=20.0, pool=5.0)
                )

                logger.debug(f"[{self.plugin_name}] 视频解析API响应状态码: {response.status_code}")
                if response.status_code != 200:
                    logger.warning(f"[{self.plugin_name}] 视频解析API返回错误状态码: {response.status_code}")
                    return None

                # 解析响应
                try:
                    result = response.json()
                    logger.debug(f"[{self.plugin_name}] 视频解析API响应: {result}")

                    # 检查响应格式和状态码
                    if not isinstance(result, dict):
                        logger.warning(f"[{self.plugin_name}] 视频解析API返回格式错误")
                        return None

                    # 检查API返回的状态码
                    if result.get("code") != 200:
                        logger.warning(f"[{self.plugin_name}] 视频解析API返回错误代码: {result.get('code')}")
                        return None

                    # 提取data字段中的视频信息
                    data = result.get("data", {})
                    if not data:
                        logger.warning(f"[{self.plugin_name}] 视频解析API返回空数据")
                        return None

                    # 提取视频信息（适配苏苏API响应格式）
                    video_info = {
                        "media_url": data.get("url", ""),
                        "artist_name": data.get("author", "抖音用户"),  # 苏苏API提供作者信息
                        "song_name": data.get("title", "未知标题"),
                        "img_url": data.get("cover", "")
                    }

                    logger.debug(f"[{self.plugin_name}] 提取的视频信息: {video_info}")

                    # 验证必要字段
                    if not video_info["media_url"]:
                        logger.warning(f"[{self.plugin_name}] 解析结果缺少视频URL")
                        return None

                    logger.info(f"[{self.plugin_name}] 视频解析成功: {video_info['song_name']} by {video_info['artist_name']}")
                    return video_info

                except json.JSONDecodeError:
                    logger.error(f"[{self.plugin_name}] 视频解析API响应JSON解析失败")
                    return None

            except httpx.RequestError as e:
                logger.error(f"[{self.plugin_name}] 视频解析请求失败: {str(e)}")
                return None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 视频解析异常: {str(e)}")
            return None

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        start_time = time.time()  # 性能监控开始时间
        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是视频搜索命令、选择命令或管理员命令
        command = content.split()
        is_video_command = command[0] in self.command if command else False
        is_select_command = command[0] in self.select_command if command else False
        is_admin_command = command[0] in self.admin_command if command else False

        # 检查是否是纯数字（直接选择视频）
        is_number_select = False
        if len(command) == 1 and command[0].isdigit():
            is_number_select = True

        if not is_video_command and not is_select_command and not is_admin_command and not is_number_select:
            return

        # 处理管理员命令
        if is_admin_command:
            await self._handle_admin_command(bot, message, command)
            return

        # 处理视频选择命令
        if is_select_command:
            await self._handle_select_command(bot, message, command)
            return

        # 处理纯数字选择
        if is_number_select:
            # 构造选择命令格式
            select_command = ["播放", command[0]]
            await self._handle_select_command(bot, message, select_command)
            return

        # 发送确认表情包（仅对视频搜索命令）
        if is_video_command and self.confirm_emoji:
            await bot.send_emoji_message(wxid, self.emoji_md5, self.emoji_size)

        logger.info(f"[{self.plugin_name}] 收到视频搜索请求: {content} from {user_wxid}")

        # 如果是视频搜索命令但没有参数
        if is_video_command and len(command) == 1:
            await bot.send_at_message(
                message["FromWxid"],
                f"{self.command_format}",
                [message["SenderWxid"]]
            )
            return

        try:
            logger.debug(f"[{self.plugin_name}] 开始处理视频搜索请求，用户: {user_wxid}")

            # 提取搜索关键词
            query = " ".join(command[1:]).strip()
            if not query:
                logger.debug(f"[{self.plugin_name}] 搜索关键词为空")
                await bot.send_at_message(
                    message["FromWxid"],
                    "请输入要搜索的视频关键词",
                    [message["SenderWxid"]]
                )
                return

            logger.info(f"[{self.plugin_name}] 开始搜索视频，关键词: '{query}'")
            # 调用视频搜索API
            result = await self.call_doubao_video_search_api(bot, message, query)

            if not result:
                logger.warning(f"[{self.plugin_name}] 视频搜索API返回空结果")
                await bot.send_at_message(message["FromWxid"], "❌ 视频搜索失败，请稍后重试", [message["SenderWxid"]])
                return

            logger.debug(f"[{self.plugin_name}] 开始处理搜索结果")
            await self.handle_video_message(bot, message, result, query)

            # 记录处理完成时间和性能指标
            end_time = time.time()
            processing_time = end_time - start_time
            logger.info(f"[{self.plugin_name}] 视频搜索处理完成，耗时: {processing_time:.2f}秒")

            # 性能监控已移除

        except Exception as e:
            end_time = time.time()
            processing_time = end_time - start_time
            logger.error(f"[{self.plugin_name}] 视频搜索处理失败，耗时: {processing_time:.2f}秒，错误: {str(e)}")

            # 性能监控已移除

            await bot.send_at_message(message["FromWxid"], "出问题了，等会再试试", [message["SenderWxid"]])

    async def on_disable(self):
        """插件禁用时调用"""
        await self.close_session()

    async def get_session(self):
        """获取或创建httpx异步客户端"""
        async with self._client_lock:
            if self._client is None or self._client.is_closed:
                self._client = await get_httpx_client()
            return self._client

    async def close_session(self):
        """关闭httpx客户端"""
        async with self._client_lock:
            if self._client and not self._client.is_closed:
                await self._client.aclose()
                self._client = None

    def _generate_headers(self) -> dict:
        """生成请求头

        Returns:
            dict: 请求头字典
        """
        # 生成随机的x-flow-trace
        flow_trace = f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01"

        return {
            "Host": "www.doubao.com",
            "Connection": "keep-alive",
            "x-flow-trace": flow_trace,  # 使用动态生成的值
            "sec-ch-ua-platform": '"Android"',
            "sec-ch-ua": '"Chromium";v="130", "Android WebView";v="130", "Not?A_Brand";v="99"',
            "sec-ch-ua-mobile": "?1",
            "Agw-Js-Conv": "str, str",
            "last-event-id": "undefined",
            "User-Agent": "Mozilla/5.0 (Linux; Android 15; PJD110 Build/AP3A.240617.008) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36",
            "content-type": "application/json",  # 改为小写，与doubao_chat.py一致
            "Accept": "*/*",
            "Origin": "https://www.doubao.com",
            "X-Requested-With": "mark.via",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://www.doubao.com/chat/",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Cookie": self.cookies
        }

    async def _handle_admin_command(self, bot: WechatAPIClient, message: dict, command: List[str]):
        """处理管理员命令

        Args:
            bot: 微信API客户端
            message: 消息对象
            command: 命令列表
        """
        cmd = command[0]

        if cmd in ["视频搜索状态", "视频搜索报告"]:
            try:
                # 生成简单的状态报告
                report = f"""🎬 豆包视频搜索插件状态报告

📊 基本信息:
- 插件状态: {'启用' if self.enable else '禁用'}
- 限制机制: 已禁用

🔧 配置信息:
- 设备ID: {self.device_id}
- Web ID: {self.web_id}
- Cookies配置: {'已配置' if self.cookies else '未配置'}

📈 缓存状态:
- 视频缓存数量: {len(self._video_cache)}
- 搜索结果缓存: {len(self._search_results)}

⚙️ 网络状态:
- HTTP客户端: {'已连接' if self._client and not self._client.is_closed else '未连接'}
"""

                await bot.send_text_message(message["FromWxid"], report)

            except Exception as e:
                logger.error(f"[{self.plugin_name}] 生成状态报告失败: {str(e)}")
                await bot.send_text_message(message["FromWxid"], f"生成状态报告失败: {str(e)}")

        else:
            await bot.send_text_message(message["FromWxid"], "未知的管理员命令")

    async def _handle_select_command(self, bot: WechatAPIClient, message: dict, command: List[str]):
        """处理视频选择命令

        Args:
            bot: 微信API客户端
            message: 消息对象
            command: 命令列表
        """
        try:
            if len(command) < 2:
                await bot.send_at_message(
                    message["FromWxid"],
                    "请输入要播放的视频序号，例如：播放 1",
                    [message["SenderWxid"]]
                )
                return

            # 解析视频序号
            try:
                index = int(command[1])
                if index < 1:
                    await bot.send_at_message(
                        message["FromWxid"],
                        "视频序号必须大于0",
                        [message["SenderWxid"]]
                    )
                    return
            except ValueError:
                await bot.send_at_message(
                    message["FromWxid"],
                    "请输入正确的数字序号",
                    [message["SenderWxid"]]
                )
                return



            # 处理视频选择
            await self.handle_video_selection(bot, message, index)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理选择命令异常: {str(e)}")
            await bot.send_at_message(message["FromWxid"], "选择失败，等会再试试", [message["SenderWxid"]])
